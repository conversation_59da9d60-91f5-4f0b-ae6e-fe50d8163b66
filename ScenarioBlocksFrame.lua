-- ScenarioBlocksFrame.lua
-- 将temp1.xml中的所有XML内容转换为符合World of Warcraft 3.3.5 API规范的Lua代码

-- 调试模式控制开关
local DEBUG_ENABLED = true

-- 统一调试函数
local function DebugPrint(message)
    if DEBUG_ENABLED then
        local currentTime = time()
        local gameTime = GetTime()
        local timeTable = date("*t", currentTime)
        local hours = timeTable.hour
        local minutes = timeTable.min
        local seconds = timeTable.sec
        local milliseconds = math.floor((gameTime - math.floor(gameTime)) * 1000)
        local timestamp = string.format("|cff888888[%02d:%02d:%02d.%03d]|r",
                                      hours, minutes, seconds, milliseconds)
        print(timestamp .. " " .. message)
    end
end

ScenarioBlocksFrameManager = {}

-- 创建Atlas纹理的辅助函数 - 参考ChallengesKeystoneFrameUI.lua
function ScenarioBlocksFrameManager:CreateAtlasTexture(parent, atlasName, layer, sublevel, useAtlasSize)
    local texture = parent:CreateTexture(nil, layer or "ARTWORK", nil, sublevel or 0)
    
    -- 获取Atlas纹理信息
    local atlasInfo = GetAtlasTextureInfo(atlasName)
    if atlasInfo then
        texture:SetTexture(atlasInfo.atlasPath)
        texture:SetTexCoord(atlasInfo.left, atlasInfo.right, atlasInfo.top, atlasInfo.bottom)
        
        if useAtlasSize then
            texture:SetSize(atlasInfo.width, atlasInfo.height)
        end
        DebugPrint("|cff00ff00ScenarioBlocks|r: 成功加载Atlas纹理 '" .. atlasName .. "'")
    else
        DebugPrint("|cffff0000ScenarioBlocks|r: 无法找到Atlas纹理: " .. atlasName)
        -- 设置一个默认纹理或颜色作为后备
        texture:SetColorTexture(1, 1, 1, 0.5) -- 白色半透明作为占位符
    end
    
    return texture
end

-- 主函数：创建ScenarioBlocksFrame及其所有子框架
-- @param parentFrame Frame 父框架（可以是ObjectiveTrackerBlocksFrame或其他）
-- @param frameName string 框架名称（可选）
-- @return Frame 创建的主框架对象
function ScenarioBlocksFrameManager:CreateScenarioBlocksFrame(parentFrame, frameName)
    DebugPrint("|cff00ff00ScenarioBlocks|r: 开始创建ScenarioBlocksFrame")
    
    local name = frameName or "ScenarioBlocksFrame"
    
    -- 创建主滚动框架 - 对应XML中的ScrollFrame
    local scrollFrame = CreateFrame("ScrollFrame", name, parentFrame)
    scrollFrame:SetSize(212, 10) -- 对应XML中的Size x="212" y="10"
    scrollFrame:Hide() -- 对应XML中的hidden="true"
    
    DebugPrint("|cff00ff00ScenarioBlocks|r: 主滚动框架已创建")
    
    -- 创建滚动子框架 - 对应XML中的ScrollChild
    local scrollChild = CreateFrame("Frame", nil, scrollFrame)
    scrollChild:SetSize(10, 10) -- 对应XML中的Size x="10" y="10"
    scrollFrame:SetScrollChild(scrollChild)
    scrollFrame.ScrollContents = scrollChild -- 对应XML中的parentKey="ScrollContents"
    
    DebugPrint("|cff00ff00ScenarioBlocks|r: 滚动子框架已创建")
    
    -- 创建所有子框架
    self:CreateScenarioObjectiveBlock(scrollChild)
    self:CreateScenarioStageBlock(scrollChild)
    self:CreateScenarioChallengeModeBlock(scrollChild)
    self:CreateScenarioProvingGroundsBlock(scrollChild)
    
    -- 设置脚本事件
    self:SetupScenarioBlocksFrameScripts(scrollFrame)
    
    DebugPrint("|cff00ff00ScenarioBlocks|r: ScenarioBlocksFrame创建完成")
    return scrollFrame
end

-- 创建ScenarioObjectiveBlock框架
function ScenarioBlocksFrameManager:CreateScenarioObjectiveBlock(parent)
    DebugPrint("|cff00ff00ScenarioBlocks|r: 创建ScenarioObjectiveBlock")
    
    local frame = CreateFrame("Frame", "ScenarioObjectiveBlock", parent)
    frame:SetSize(192, 10) -- 对应XML中的Size x="192" y="10"
    frame:Hide() -- 对应XML中的hidden="true"
    
    parent.ScenarioObjectiveBlock = frame
    return frame
end

-- 创建ScenarioStageBlock框架
function ScenarioBlocksFrameManager:CreateScenarioStageBlock(parent)
    DebugPrint("|cff00ff00ScenarioBlocks|r: 创建ScenarioStageBlock")
    
    local frame = CreateFrame("Frame", "ScenarioStageBlock", parent)
    frame:SetSize(201, 83) -- 对应XML中的Size x="201" y="83"
    frame:Hide() -- 对应XML中的hidden="true"
    
    -- 创建层级结构
    self:CreateScenarioStageBlockLayers(frame)
    self:CreateScenarioStageBlockFrames(frame)
    self:SetupScenarioStageBlockScripts(frame)
    
    parent.ScenarioStageBlock = frame
    return frame
end

-- 创建ScenarioStageBlock的层级结构
function ScenarioBlocksFrameManager:CreateScenarioStageBlockLayers(frame)
    -- BACKGROUND层
    local normalBG = self:CreateAtlasTexture(frame, "ScenarioTrackerToast", "BACKGROUND", 0, true)
    normalBG:SetPoint("TOPLEFT", frame, "TOPLEFT", 0, 0)
    frame.NormalBG = normalBG -- 对应XML中的parentKey="NormalBG"
    
    -- BORDER层
    local finalBG = self:CreateAtlasTexture(frame, "ScenarioTrackerToast-FinalFiligree", "BORDER", 0, true)
    finalBG:SetPoint("TOPLEFT", frame, "TOPLEFT", 4, -4)
    frame.FinalBG = finalBG -- 对应XML中的parentKey="FinalBG"
    
    local glowTexture = self:CreateAtlasTexture(frame, "ScenarioTrackerToast", "BORDER", 0, true)
    glowTexture:SetPoint("TOPLEFT", frame, "TOPLEFT", 0, 0)
    glowTexture:SetAlpha(0) -- 对应XML中的alpha="0"
    glowTexture:SetBlendMode("ADD") -- 对应XML中的alphaMode="ADD"
    frame.GlowTexture = glowTexture -- 对应XML中的parentKey="GlowTexture"
    
    -- 创建GlowTexture的动画
    self:CreateGlowTextureAnimation(glowTexture)
    
    -- ARTWORK层 - 字体字符串
    self:CreateScenarioStageBlockFontStrings(frame)
end

-- 创建ScenarioStageBlock的字体字符串
function ScenarioBlocksFrameManager:CreateScenarioStageBlockFontStrings(frame)
    -- Stage字体字符串
    local stage = frame:CreateFontString(nil, "ARTWORK", "QuestTitleFont")
    stage:SetSize(172, 18) -- 对应XML中的Size x="172" y="18"
    stage:SetPoint("TOPLEFT", frame, "TOPLEFT", 15, -10)
    stage:SetJustifyH("LEFT") -- 对应XML中的justifyH="LEFT"
    stage:SetWordWrap(true) -- 对应XML中的wordwrap="true"
    stage:SetTextColor(1, 0.914, 0.682) -- 对应XML中的Color r="1" g="0.914" b="0.682"
    stage:SetShadowColor(0, 0, 0) -- 对应XML中的Shadow Color
    stage:SetShadowOffset(1, -1) -- 对应XML中的Shadow Offset
    frame.Stage = stage -- 对应XML中的parentKey="Stage"
    
    -- CompleteLabel字体字符串
    local completeLabel = frame:CreateFontString(nil, "ARTWORK", "QuestTitleFont")
    completeLabel:SetPoint("LEFT", frame, "LEFT", 15, 3)
    completeLabel:SetText("STAGE_COMPLETE") -- 对应XML中的text="STAGE_COMPLETE"
    completeLabel:Hide() -- 对应XML中的hidden="true"
    completeLabel:SetTextColor(1, 0.914, 0.682) -- 对应XML中的Color
    completeLabel:SetShadowColor(0, 0, 0) -- 对应XML中的Shadow Color
    completeLabel:SetShadowOffset(1, -1) -- 对应XML中的Shadow Offset
    frame.CompleteLabel = completeLabel -- 对应XML中的parentKey="CompleteLabel"
    
    -- Name字体字符串
    local name = frame:CreateFontString(nil, "ARTWORK", "GameFontNormal")
    name:SetSize(172, 28) -- 对应XML中的Size x="172" y="28"
    name:SetPoint("TOPLEFT", stage, "BOTTOMLEFT", 0, -4) -- 对应XML中的相对定位
    name:SetJustifyH("LEFT") -- 对应XML中的justifyH="LEFT"
    name:SetJustifyV("TOP") -- 对应XML中的justifyV="TOP"
    name:SetSpacing(2) -- 对应XML中的spacing="2"
    name:SetTextColor(1, 0.831, 0.380) -- 对应XML中的Color r="1" g="0.831" b="0.380"
    frame.Name = name -- 对应XML中的parentKey="Name"
end

-- 创建GlowTexture的动画组
function ScenarioBlocksFrameManager:CreateGlowTextureAnimation(glowTexture)
    local animGroup = glowTexture:CreateAnimationGroup()
    glowTexture.AlphaAnim = animGroup -- 对应XML中的parentKey="AlphaAnim"
    
    -- 第一个Alpha动画
    local alpha1 = animGroup:CreateAnimation("Alpha")
    alpha1:SetFromAlpha(0) -- 对应XML中的fromAlpha="0"
    alpha1:SetToAlpha(1) -- 对应XML中的toAlpha="1"
    alpha1:SetDuration(0.266) -- 对应XML中的duration="0.266"
    alpha1:SetOrder(1) -- 对应XML中的order="1"
    
    -- 第二个Alpha动画
    local alpha2 = animGroup:CreateAnimation("Alpha")
    alpha2:SetEndDelay(0.2) -- 对应XML中的endDelay="0.2"
    alpha2:SetFromAlpha(1) -- 对应XML中的fromAlpha="1"
    alpha2:SetToAlpha(0) -- 对应XML中的toAlpha="0"
    alpha2:SetDuration(0.333) -- 对应XML中的duration="0.333"
    alpha2:SetOrder(2) -- 对应XML中的order="2"
end

-- 创建ScenarioStageBlock的子框架
function ScenarioBlocksFrameManager:CreateScenarioStageBlockFrames(frame)
    -- 创建RewardButton
    local rewardButton = CreateFrame("Button", nil, frame)
    rewardButton:SetSize(48, 48) -- 对应XML中的Size x="48" y="48"
    rewardButton:SetPoint("BOTTOMRIGHT", frame, "BOTTOMRIGHT", 50, -3)
    rewardButton:Hide() -- 对应XML中的hidden="true"
    frame.RewardButton = rewardButton -- 对应XML中的parentKey="RewardButton"
    
    -- 创建RewardButton的纹理层
    self:CreateRewardButtonLayers(rewardButton)
    self:SetupRewardButtonScripts(rewardButton)
end

-- 创建RewardButton的纹理层
function ScenarioBlocksFrameManager:CreateRewardButtonLayers(button)
    -- OVERLAY层 textureSubLevel="1" - RewardRing
    local rewardRing = self:CreateAtlasTexture(button, "legioninvasion-scenario-rewardring", "OVERLAY", 1, true)
    rewardRing:SetPoint("CENTER", button, "CENTER") -- 对应XML中的相对定位
    button.RewardRing = rewardRing -- 对应XML中的parentKey="RewardRing"
    
    -- OVERLAY层 textureSubLevel="0" - RewardIcon
    local rewardIcon = button:CreateTexture(nil, "OVERLAY", nil, 0)
    rewardIcon:SetSize(29, 29) -- 对应XML中的Size x="29" y="29"
    rewardIcon:SetPoint("CENTER", rewardRing, "CENTER") -- 对应XML中的相对定位
    button.RewardIcon = rewardIcon -- 对应XML中的parentKey="RewardIcon"
end

-- 设置RewardButton的脚本事件
function ScenarioBlocksFrameManager:SetupRewardButtonScripts(button)
    -- OnEnter事件
    button:SetScript("OnEnter", function(self)
        if ScenarioRewardButton_OnEnter then
            ScenarioRewardButton_OnEnter(self)
        else
            DebugPrint("|cffff0000ScenarioBlocks|r: ScenarioRewardButton_OnEnter函数未定义")
        end
    end)
    
    -- OnLeave事件
    button:SetScript("OnLeave", function(self)
        if ScenarioRewardButton_OnLeave then
            ScenarioRewardButton_OnLeave(self)
        else
            DebugPrint("|cffff0000ScenarioBlocks|r: ScenarioRewardButton_OnLeave函数未定义")
        end
    end)
end

-- 设置ScenarioStageBlock的脚本事件
function ScenarioBlocksFrameManager:SetupScenarioStageBlockScripts(frame)
    -- OnLoad事件
    frame:SetScript("OnLoad", function(self)
        -- 对应XML中的OnLoad脚本
        if self.Stage then
            -- 设置字体对象尝试列表
            -- 注意：在WoW 3.3.5中可能需要使用不同的方法
            if self.Stage.SetFontObjectsToTry then
                self.Stage:SetFontObjectsToTry("QuestTitleFont", "Fancy16Font", "SystemFont_Med1")
            else
                -- 备用方法：直接设置字体对象
                self.Stage:SetFontObject("QuestTitleFont")
            end
        end
    end)
    
    -- OnEnter事件
    frame:SetScript("OnEnter", function(self)
        if ScenarioObjectiveStageBlock_OnEnter then
            ScenarioObjectiveStageBlock_OnEnter(self)
        else
            DebugPrint("|cffff0000ScenarioBlocks|r: ScenarioObjectiveStageBlock_OnEnter函数未定义")
        end
    end)
    
    -- OnLeave事件
    frame:SetScript("OnLeave", function(self)
        GameTooltip:Hide() -- 对应XML中的OnLeave脚本
    end)
end

-- 创建ScenarioChallengeModeBlock框架
function ScenarioBlocksFrameManager:CreateScenarioChallengeModeBlock(parent)
    DebugPrint("|cff00ff00ScenarioBlocks|r: 创建ScenarioChallengeModeBlock")

    local frame = CreateFrame("Frame", "ScenarioChallengeModeBlock", parent)
    frame:SetSize(251, 87) -- 对应XML中的Size x="251" y="87"
    frame:Hide() -- 对应XML中的hidden="true"

    -- 创建层级结构
    self:CreateChallengeModeBlockLayers(frame)
    self:CreateChallengeModeBlockFrames(frame)

    parent.ScenarioChallengeModeBlock = frame
    return frame
end

-- 创建ScenarioChallengeModeBlock的层级结构
function ScenarioBlocksFrameManager:CreateChallengeModeBlockLayers(frame)
    -- BACKGROUND层
    local timerBGBack = self:CreateAtlasTexture(frame, "ChallengeMode-TimerBG-Back", "BACKGROUND", 0, true)
    timerBGBack:SetPoint("BOTTOM", frame, "BOTTOM", 0, 13)
    frame.TimerBGBack = timerBGBack -- 对应XML中的parentKey="TimerBGBack"

    -- BACKGROUND层 textureSubLevel="1"
    local timerBG = self:CreateAtlasTexture(frame, "ChallengeMode-TimerBG", "BACKGROUND", 1, true)
    timerBG:SetPoint("BOTTOM", frame, "BOTTOM", 0, 13)
    frame.TimerBG = timerBG -- 对应XML中的parentKey="TimerBG"

    -- OVERLAY层
    local overlayTexture = self:CreateAtlasTexture(frame, "challengemode-timer", "OVERLAY", 0, false)
    overlayTexture:SetAllPoints(frame) -- 对应XML中的setAllPoints="true"

    -- OVERLAY层字体字符串
    local level = frame:CreateFontString(nil, "OVERLAY", "GameFontNormalMed2")
    level:SetPoint("TOPLEFT", frame, "TOPLEFT", 28, -18)
    level:SetJustifyH("LEFT") -- 对应XML中的justifyH="LEFT"
    frame.Level = level -- 对应XML中的parentKey="Level"

    local timeLeft = frame:CreateFontString(nil, "OVERLAY", "GameFontHighlightHuge")
    timeLeft:SetPoint("TOPLEFT", level, "BOTTOMLEFT", 0, -8) -- 对应XML中的相对定位
    timeLeft:SetJustifyH("LEFT") -- 对应XML中的justifyH="LEFT"
    frame.TimeLeft = timeLeft -- 对应XML中的parentKey="TimeLeft"
end

-- 创建ScenarioChallengeModeBlock的子框架
function ScenarioBlocksFrameManager:CreateChallengeModeBlockFrames(frame)
    -- 创建StartedDepleted框架
    self:CreateStartedDepletedFrame(frame)

    -- 创建TimesUpLootStatus框架
    self:CreateTimesUpLootStatusFrame(frame)

    -- 创建DeathCount框架
    self:CreateDeathCountFrame(frame)

    -- 创建StatusBar
    self:CreateChallengeModeStatusBar(frame)

    -- 创建词缀框架（继承ScenarioChallengeModeAffixTemplate）
    -- 注意：这里需要根据实际需求创建词缀框架
    -- local affixFrame = CreateFrame("Frame", nil, frame, "ScenarioChallengeModeAffixTemplate")
    -- affixFrame:Hide() -- 对应XML中的hidden="true"
end

-- 创建StartedDepleted框架
function ScenarioBlocksFrameManager:CreateStartedDepletedFrame(parent)
    local frame = CreateFrame("Frame", nil, parent)
    frame:SetSize(19, 20) -- 对应XML中的Size x="19" y="20"
    frame:SetPoint("LEFT", parent.Level, "RIGHT", 4, 0) -- 对应XML中的相对定位
    frame:Hide() -- 对应XML中的hidden="true"
    frame:EnableMouse(true) -- 对应XML中的enableMouse="true"
    parent.StartedDepleted = frame -- 对应XML中的parentKey="StartedDepleted"

    -- ARTWORK层
    local chestTexture = self:CreateAtlasTexture(frame, "ChallengeMode-icon-chest", "ARTWORK", 0, true)
    chestTexture:SetPoint("CENTER", frame, "CENTER")

    -- ARTWORK层 textureSubLevel="1"
    local redlineTexture = self:CreateAtlasTexture(frame, "ChallengeMode-icon-redline", "ARTWORK", 1, true)
    redlineTexture:SetPoint("CENTER", frame, "CENTER")

    -- 设置脚本事件
    frame:SetScript("OnEnter", function(self)
        GameTooltip:SetOwner(self, "ANCHOR_RIGHT")
        GameTooltip:SetText("CHALLENGE_MODE_DEPLETED_KEYSTONE", 1, 1, 1) -- 对应XML中的文本
        GameTooltip:AddLine("CHALLENGE_MODE_KEYSTONE_DEPLETED_AT_START", nil, nil, nil, true) -- 对应XML中的文本
        GameTooltip:Show()
    end)

    frame:SetScript("OnLeave", function(self)
        if GameTooltip_Hide then
            GameTooltip_Hide()
        else
            GameTooltip:Hide()
        end
    end)
end

-- 创建TimesUpLootStatus框架
function ScenarioBlocksFrameManager:CreateTimesUpLootStatusFrame(parent)
    local frame = CreateFrame("Frame", nil, parent)
    frame:SetSize(19, 20) -- 对应XML中的Size x="19" y="20"
    frame:SetPoint("LEFT", parent.TimeLeft, "RIGHT", 4, 0) -- 对应XML中的相对定位
    frame:Hide() -- 对应XML中的hidden="true"
    frame:EnableMouse(true) -- 对应XML中的enableMouse="true"
    parent.TimesUpLootStatus = frame -- 对应XML中的parentKey="TimesUpLootStatus"

    -- ARTWORK层
    local chestTexture = self:CreateAtlasTexture(frame, "ChallengeMode-icon-chest", "ARTWORK", 0, true)
    chestTexture:SetPoint("CENTER", frame, "CENTER")

    -- ARTWORK层 textureSubLevel="1"
    local noLootTexture = self:CreateAtlasTexture(frame, "ChallengeMode-icon-redline", "ARTWORK", 1, true)
    noLootTexture:SetPoint("CENTER", frame, "CENTER")
    frame.NoLoot = noLootTexture -- 对应XML中的parentKey="NoLoot"

    -- 设置脚本事件
    frame:SetScript("OnEnter", function(self)
        if Scenario_ChallengeMode_TimesUpLootStatus_OnEnter then
            Scenario_ChallengeMode_TimesUpLootStatus_OnEnter(self)
        else
            DebugPrint("|cffff0000ScenarioBlocks|r: Scenario_ChallengeMode_TimesUpLootStatus_OnEnter函数未定义")
        end
    end)

    frame:SetScript("OnLeave", function(self)
        if GameTooltip_Hide then
            GameTooltip_Hide()
        else
            GameTooltip:Hide()
        end
    end)
end

-- 创建DeathCount框架
function ScenarioBlocksFrameManager:CreateDeathCountFrame(parent)
    local frame = CreateFrame("Frame", nil, parent)
    frame:SetSize(20, 16) -- 对应XML中的Size x="20" y="16"
    frame:SetPoint("TOPLEFT", parent, "BOTTOMRIGHT", -47, 43) -- 对应XML中的相对定位
    frame:Hide() -- 对应XML中的hidden="true"
    frame:EnableMouse(true) -- 对应XML中的enableMouse="true"
    parent.DeathCount = frame -- 对应XML中的parentKey="DeathCount"

    -- 注意：XML中有mixin="ScenarioChallengeDeathCountMixin"，这里需要手动实现相关功能

    -- ARTWORK层
    local icon = self:CreateAtlasTexture(frame, "poi-graveyard-neutral", "ARTWORK", 0, true)
    icon:SetPoint("LEFT", frame, "LEFT")
    frame.Icon = icon -- 对应XML中的parentKey="Icon"

    local count = frame:CreateFontString(nil, "ARTWORK", "GameFontHighlightSmall2")
    count:SetPoint("LEFT", icon, "RIGHT", 1, 0) -- 对应XML中的相对定位
    frame.Count = count -- 对应XML中的parentKey="Count"

    -- 设置脚本事件（对应XML中的mixin方法）
    frame:SetScript("OnLoad", function(self)
        -- 对应XML中的OnLoad method="OnLoad"
        DebugPrint("|cff00ff00ScenarioBlocks|r: DeathCount OnLoad")
    end)

    frame:SetScript("OnEvent", function(self, event, ...)
        -- 对应XML中的OnEvent method="OnEvent"
        DebugPrint("|cff00ff00ScenarioBlocks|r: DeathCount OnEvent: " .. event)
    end)

    frame:SetScript("OnEnter", function(self)
        -- 对应XML中的OnEnter method="OnEnter"
        DebugPrint("|cff00ff00ScenarioBlocks|r: DeathCount OnEnter")
    end)

    frame:SetScript("OnLeave", function(self)
        if GameTooltip_Hide then
            GameTooltip_Hide()
        else
            GameTooltip:Hide()
        end
    end)
end

-- 创建挑战模式状态栏
function ScenarioBlocksFrameManager:CreateChallengeModeStatusBar(parent)
    local statusBar = CreateFrame("StatusBar", nil, parent)
    statusBar:SetSize(207, 13) -- 对应XML中的Size x="207" y="13"
    statusBar:SetPoint("BOTTOM", parent, "BOTTOM", 0, 10)
    -- statusBar:SetParentLevel(true) -- 对应XML中的useParentLevel="true"，在3.3.5中可能不支持
    parent.StatusBar = statusBar -- 对应XML中的parentKey="StatusBar"

    -- 设置状态栏纹理
    local barTexture = self:CreateAtlasTexture(statusBar, "ChallengeMode-TimerFill", "ARTWORK", 0, false)
    statusBar:SetStatusBarTexture(barTexture) -- 对应XML中的BarTexture atlas="ChallengeMode-TimerFill"
end

-- 创建ScenarioProvingGroundsBlock框架
function ScenarioBlocksFrameManager:CreateScenarioProvingGroundsBlock(parent)
    DebugPrint("|cff00ff00ScenarioBlocks|r: 创建ScenarioProvingGroundsBlock")

    local frame = CreateFrame("Frame", "ScenarioProvingGroundsBlock", parent)
    frame:SetSize(201, 77) -- 对应XML中的Size x="201" y="77"
    frame:Hide() -- 对应XML中的hidden="true"

    -- 创建层级结构
    self:CreateProvingGroundsBlockLayers(frame)
    self:CreateProvingGroundsBlockFrames(frame)

    parent.ScenarioProvingGroundsBlock = frame
    return frame
end

-- 创建ScenarioProvingGroundsBlock的层级结构
function ScenarioBlocksFrameManager:CreateProvingGroundsBlockLayers(frame)
    -- BACKGROUND层
    local bg = self:CreateAtlasTexture(frame, "ScenarioTrackerToast", "BACKGROUND", 0, true)
    bg:SetPoint("TOPLEFT", frame, "TOPLEFT", 0, 0)
    frame.BG = bg -- 对应XML中的parentKey="BG"

    -- BORDER层
    local goldCurlies = self:CreateAtlasTexture(frame, "ScenarioTrackerToast-FinalFiligree", "BORDER", 0, true)
    goldCurlies:SetPoint("TOPLEFT", frame, "TOPLEFT", 4, -4)
    frame.GoldCurlies = goldCurlies -- 对应XML中的parentKey="GoldCurlies"

    -- ARTWORK层
    self:CreateProvingGroundsArtworkLayer(frame)
end

-- 创建ScenarioProvingGroundsBlock的ARTWORK层
function ScenarioBlocksFrameManager:CreateProvingGroundsArtworkLayer(frame)
    -- MedalIcon纹理
    local medalIcon = frame:CreateTexture(nil, "ARTWORK")
    medalIcon:SetSize(52, 52) -- 对应XML中的Size x="52" y="52"
    medalIcon:SetPoint("LEFT", frame, "LEFT", 5, -1)
    medalIcon:SetBlendMode("ADD") -- 对应XML中的alphaMode="ADD"
    medalIcon:SetTexture("Interface\\Challenges\\challenges-plat") -- 对应XML中的file属性
    frame.MedalIcon = medalIcon -- 对应XML中的parentKey="MedalIcon"

    -- WaveLabel字体字符串
    local waveLabel = frame:CreateFontString(nil, "ARTWORK", "QuestFont_Large")
    waveLabel:SetPoint("TOPLEFT", medalIcon, "TOPRIGHT", 1, -4)
    waveLabel:SetText("PROVING_GROUNDS_WAVE") -- 对应XML中的text属性
    waveLabel:SetTextColor(1.0, 0.82, 0) -- 对应XML中的Color r="1.0" g="0.82" b="0"
    waveLabel:SetShadowColor(0, 0, 0) -- 对应XML中的Shadow Color
    waveLabel:SetShadowOffset(1, -1) -- 对应XML中的Shadow Offset
    frame.WaveLabel = waveLabel -- 对应XML中的parentKey="WaveLabel"

    -- Wave字体字符串
    local wave = frame:CreateFontString(nil, "ARTWORK", "GameFontHighlightLarge")
    wave:SetPoint("BOTTOMLEFT", waveLabel, "BOTTOMRIGHT", 4, -1)
    wave:SetText("0") -- 对应XML中的text="0"
    frame.Wave = wave -- 对应XML中的parentKey="Wave"

    -- ScoreLabel字体字符串
    local scoreLabel = frame:CreateFontString(nil, "ARTWORK", "QuestFont_Large")
    scoreLabel:SetPoint("TOPLEFT", waveLabel, "BOTTOMLEFT", 0, -3)
    scoreLabel:SetText("PROVING_GROUNDS_SCORE") -- 对应XML中的text属性
    scoreLabel:Hide() -- 对应XML中的hidden="true"
    scoreLabel:SetTextColor(1.0, 0.82, 0) -- 对应XML中的Color
    scoreLabel:SetShadowColor(0, 0, 0) -- 对应XML中的Shadow Color
    scoreLabel:SetShadowOffset(1, -1) -- 对应XML中的Shadow Offset
    frame.ScoreLabel = scoreLabel -- 对应XML中的parentKey="ScoreLabel"

    -- Score字体字符串
    local score = frame:CreateFontString(nil, "ARTWORK", "GameFontHighlightLarge")
    score:SetPoint("BOTTOMLEFT", scoreLabel, "BOTTOMRIGHT", 4, -1)
    score:SetText("0") -- 对应XML中的text="0"
    score:Hide() -- 对应XML中的hidden="true"
    frame.Score = score -- 对应XML中的parentKey="Score"
end

-- 创建ScenarioProvingGroundsBlock的子框架
function ScenarioBlocksFrameManager:CreateProvingGroundsBlockFrames(frame)
    -- 创建StatusBar
    local statusBar = CreateFrame("StatusBar", nil, frame)
    statusBar:SetSize(177, 15) -- 对应XML中的Size x="177" y="15"
    -- statusBar:SetParentLevel(true) -- 对应XML中的useParentLevel="true"，在3.3.5中可能不支持
    frame.StatusBar = statusBar -- 对应XML中的parentKey="StatusBar"

    -- 创建StatusBar的层级
    self:CreateProvingGroundsStatusBarLayers(statusBar)

    -- 设置状态栏纹理和颜色
    statusBar:SetStatusBarTexture("Interface\\TargetingFrame\\UI-StatusBar") -- 对应XML中的BarTexture file
    statusBar:SetStatusBarColor(0, 0.33, 0.61) -- 对应XML中的BarColor r="0" g="0.33" b="0.61"
end

-- 创建试炼场状态栏的层级
function ScenarioBlocksFrameManager:CreateProvingGroundsStatusBarLayers(statusBar)
    -- OVERLAY层
    local borderTexture = self:CreateAtlasTexture(statusBar, "challenges-timerborder", "OVERLAY", 0, false)
    borderTexture:SetSize(184, 25) -- 对应XML中的Size x="184" y="25"
    borderTexture:SetPoint("CENTER", statusBar, "CENTER", 0, 0)

    local timeLeft = statusBar:CreateFontString(nil, "OVERLAY", "GameFontHighlight")
    timeLeft:SetJustifyH("CENTER") -- 对应XML中的justifyH="CENTER"
    statusBar.TimeLeft = timeLeft -- 对应XML中的parentKey="TimeLeft"
end

-- 设置ScenarioBlocksFrame的脚本事件
function ScenarioBlocksFrameManager:SetupScenarioBlocksFrameScripts(frame)
    -- OnLoad事件
    frame:SetScript("OnLoad", function(self)
        if ScenarioBlocksFrame_OnLoad then
            ScenarioBlocksFrame_OnLoad(self)
        else
            DebugPrint("|cffff0000ScenarioBlocks|r: ScenarioBlocksFrame_OnLoad函数未定义")
        end
    end)

    -- OnEvent事件
    frame:SetScript("OnEvent", function(self, event, ...)
        if ScenarioBlocksFrame_OnEvent then
            ScenarioBlocksFrame_OnEvent(self, event, ...)
        else
            DebugPrint("|cffff0000ScenarioBlocks|r: ScenarioBlocksFrame_OnEvent函数未定义")
        end
    end)
end

-- 便捷函数：创建ScenarioBlocksFrame实例
-- @param parentFrame Frame 父框架
-- @param frameName string 框架名称（可选）
-- @return Frame 创建的ScenarioBlocksFrame
function CreateScenarioBlocksFrame(parentFrame, frameName)
    if not parentFrame then
        DebugPrint("|cffff0000ScenarioBlocks|r: 错误 - 父框架不能为空")
        return nil
    end

    return ScenarioBlocksFrameManager:CreateScenarioBlocksFrame(parentFrame, frameName)
end

-- 全局测试框架变量
local testScenarioFrame = nil

-- 注册斜杠命令的函数
function ScenarioBlocksFrameManager:RegisterSlashCommands()
    -- 注册 /scenario 命令
    SLASH_SCENARIOTEST1 = "/scenario"
    SLASH_SCENARIOTEST2 = "/testscenario"

    SlashCmdList["SCENARIOTEST"] = function(msg)
        DebugPrint("ScenarioBlocks: 收到命令参数: '" .. (msg or "nil") .. "'")
        local cmd = string.lower(msg or "")

        if cmd == "show" or cmd == "" then
            -- 创建或显示测试框架
            if not testScenarioFrame then
                DebugPrint("|cff00ff00ScenarioBlocks|r: 开始创建测试框架...")
                testScenarioFrame = CreateScenarioBlocksFrame(UIParent, "TestScenarioBlocksFrame")
                if testScenarioFrame then
                    testScenarioFrame:SetPoint("CENTER", UIParent, "CENTER", 0, 0)
                    DebugPrint("|cff00ff00ScenarioBlocks|r: 测试框架已创建")
                else
                    DebugPrint("|cffff0000ScenarioBlocks|r: 测试框架创建失败")
                    return
                end
            end
            testScenarioFrame:Show()
            DebugPrint("|cff00ff00ScenarioBlocks|r: 测试框架已显示")

        elseif cmd == "hide" then
            -- 隐藏测试框架
            if testScenarioFrame then
                testScenarioFrame:Hide()
                DebugPrint("|cff00ff00ScenarioBlocks|r: 测试框架已隐藏")
            else
                DebugPrint("|cffff0000ScenarioBlocks|r: 测试框架不存在")
            end

        elseif cmd == "debug on" then
            -- 开启调试模式
            DEBUG_ENABLED = true
            print("|cff00ff00ScenarioBlocks|r: 调试模式已开启")

        elseif cmd == "debug off" then
            -- 关闭调试模式
            DEBUG_ENABLED = false
            print("|cff00ff00ScenarioBlocks|r: 调试模式已关闭")

        else
            -- 显示帮助信息
            print("|cff00ff00ScenarioBlocks 测试命令:|r")
            print("  /scenario show - 显示测试框架")
            print("  /scenario hide - 隐藏测试框架")
            print("  /scenario debug on - 开启调试模式")
            print("  /scenario debug off - 关闭调试模式")
        end
    end

    DebugPrint("|cff00ff00ScenarioBlocks|r: 斜杠命令已注册 - /scenario, /testscenario")
end

-- 事件处理函数
function ScenarioBlocksFrameManager:OnEvent(event, ...)
    if event == "ADDON_LOADED" then
        local addonName = ...
        DebugPrint("ScenarioBlocks addonName: " .. addonName)
        if addonName == "ExtractAtlasInfos" then
            -- 插件加载完成后的初始化
            DebugPrint("|cff00ff00ScenarioBlocks|r: 插件初始化完成")
            -- 注册斜杠命令
            self:RegisterSlashCommands()
        end
    end
end

-- 创建框架用于事件处理
local eventFrame = CreateFrame("Frame")
eventFrame:SetScript("OnEvent", function(_, event, ...)
    ScenarioBlocksFrameManager:OnEvent(event, ...)
end)

-- 注册事件
eventFrame:RegisterEvent("ADDON_LOADED")
